import { injectable, Lifecycle, scoped } from 'tsyringe';
import { Auth<PERSON>and<PERSON> } from './auth.handler';
import { SystemRepo } from '../core/repositories/system';

@scoped(Lifecycle.ContainerScoped)
@injectable()
export class MigrationHandler {
  constructor(
    private readonly authHandler: AuthHandler,
    private readonly systemRepo: SystemRepo,
  ) {}

  async findSystemNeedingMigration() {
    const customerId = await this.authHandler.getLoggedInCustomerId({ redirectToLogin: false });
    if (!customerId) return undefined;

    const systems = await this.systemRepo.getAllSystems(customerId);

    return systems?.find(system => system.requiresMigration);
  }
}
